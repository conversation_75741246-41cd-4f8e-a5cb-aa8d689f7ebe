<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <TextView
            android:id="@+id/nameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="22sp"
            android:textStyle="bold"
            style="@style/Widget.CareerWorx.TextInputEditText"
            android:background="?android:colorBackground"
            android:padding="16dp"
            android:elevation="2dp" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Contact Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/emailText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:drawableStart="@android:drawable/ic_dialog_email"
                        android:drawablePadding="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/phoneText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:drawableStart="@android:drawable/ic_menu_call"
                        android:drawablePadding="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/locationText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:drawableStart="@android:drawable/ic_menu_mylocation"
                        android:drawablePadding="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Professional Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="12dp" />


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Field &amp; Specialization"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/fieldText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp"
                        android:layout_marginBottom="12dp" />


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Years of Experience"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/yearsOfExperienceText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp"
                        android:layout_marginBottom="12dp" />


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Certificate"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/certificateText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp"
                        android:layout_marginBottom="12dp" />


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Expected Salary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/expectedSalaryText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Summary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/summaryText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Skills"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/skillsText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Certificates Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Certifications"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/certificateBadgesRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/noCertificatesText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="No certifications specified"
                        android:textSize="15sp"
                        android:textColor="@color/text_secondary"
                        android:visibility="gone" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Education"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/educationText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Experience"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/experienceText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Social Links"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        style="@style/Widget.CareerWorx.TextInputEditText"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/socialLinksText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="15sp"
                        android:autoLink="web" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </LinearLayout>
</ScrollView>
